# Domain Search API

A comprehensive Node.js TypeScript application for domain name searching using the Namecheap API. This RESTful API provides domain availability checking, pricing information, and domain suggestions with built-in caching, rate limiting, and comprehensive error handling.

## Features

- **Domain Availability Checking**: Check availability of single or multiple domains
- **Pricing Information**: Get registration, renewal, and transfer pricing
- **Domain Suggestions**: Generate domain suggestions with different TLDs
- **Caching**: Built-in caching to improve performance and reduce API calls
- **Rate Limiting**: Configurable rate limiting to respect API limits
- **Input Validation**: Comprehensive validation using Joi schemas
- **Error Handling**: Structured error responses with proper HTTP status codes
- **Health Monitoring**: Health check endpoints for monitoring and deployment
- **TypeScript**: Full TypeScript support with strict type checking
- **Production Ready**: Comprehensive logging, security headers, and graceful shutdown

## Technology Stack

- **Runtime**: Node.js (v16+)
- **Framework**: Express.js
- **Language**: TypeScript
- **Validation**: Joi
- **Caching**: node-cache
- **Logging**: Winston
- **Security**: Helmet, CORS
- **API Integration**: Axios for Namecheap API

## Quick Start

### Prerequisites

- Node.js v16 or higher
- npm or yarn
- Namecheap API credentials

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd domain-search
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your Namecheap API credentials
   ```

4. **Build the project**
   ```bash
   npm run build
   ```

5. **Start the server**
   ```bash
   # Development mode with hot reload
   npm run dev:ts
   
   # Production mode
   npm start
   ```

The API will be available at `http://localhost:3000`

## Environment Configuration

### Required Variables

```env
NAMECHEAP_API_USER=your_api_user
NAMECHEAP_API_KEY=your_api_key
NAMECHEAP_USERNAME=your_username
NAMECHEAP_CLIENT_IP=your_whitelisted_ip
```

### Optional Variables

```env
NODE_ENV=development
PORT=3000
NAMECHEAP_SANDBOX=true
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
CACHE_TTL=300
LOG_LEVEL=info
```

## API Documentation

### Base URL
```
http://localhost:3000/api/v1
```

### Authentication
Currently, no authentication is required. The API uses Namecheap credentials configured via environment variables.

### Endpoints

#### 1. Search Multiple Domains
**POST** `/domains/search`

Search for availability of multiple domains with optional pricing information.

**Request Body:**
```json
{
  "domains": ["example.com", "test.org"],
  "includePricing": true,
  "tlds": [".com", ".net", ".org"]
}
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "domain": "example.com",
      "tld": ".com",
      "available": false,
      "premium": false
    },
    {
      "domain": "test.org",
      "tld": ".org", 
      "available": true,
      "premium": false,
      "pricing": {
        "registration": {
          "price": 12.99,
          "duration": 1,
          "durationType": "YEAR"
        },
        "renewal": {
          "price": 14.99,
          "duration": 1,
          "durationType": "YEAR"
        },
        "currency": "USD"
      }
    }
  ],
  "meta": {
    "totalDomains": 2,
    "availableDomains": 1,
    "unavailableDomains": 1,
    "processingTime": 1250
  }
}
```

#### 2. Check Single Domain
**GET** `/domains/check/:domain`

Check availability of a single domain.

**Parameters:**
- `domain`: Domain name to check
- `includePricing`: (optional) Include pricing information

**Example:**
```
GET /domains/check/example.com?includePricing=true
```

#### 3. Get Domain Suggestions
**GET** `/domains/suggestions/:baseDomain`

Get domain suggestions with different TLDs for a base domain.

**Parameters:**
- `baseDomain`: Base domain name (without TLD)
- `limit`: (optional) Maximum number of suggestions (default: 10)
- `includePricing`: (optional) Include pricing information

**Example:**
```
GET /domains/suggestions/mycompany?limit=5&includePricing=true
```

#### 4. Cache Management
**GET** `/domains/cache/stats` - Get cache statistics
**DELETE** `/domains/cache` - Clear cache

### Health Endpoints

#### Health Check
**GET** `/health`

Returns overall application health status.

#### Readiness Probe
**GET** `/health/ready`

Kubernetes/Docker readiness probe endpoint.

#### Liveness Probe
**GET** `/health/live`

Kubernetes/Docker liveness probe endpoint.

#### Metrics
**GET** `/health/metrics`

Basic application metrics for monitoring.

## Error Handling

The API returns structured error responses with appropriate HTTP status codes:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Validation failed",
    "details": [
      {
        "field": "domains",
        "message": "At least one domain is required"
      }
    ],
    "timestamp": "2024-01-15T10:30:00.000Z"
  }
}
```

### Error Codes

- `VALIDATION_ERROR`: Input validation failed
- `DOMAIN_INVALID`: Invalid domain format
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `NAMECHEAP_API_ERROR`: Namecheap API error
- `SERVICE_UNAVAILABLE`: Service temporarily unavailable
- `INTERNAL_ERROR`: Internal server error

## Development

### Scripts

```bash
# Development with TypeScript hot reload
npm run dev:ts

# Development with compiled JavaScript
npm run dev

# Build TypeScript to JavaScript
npm run build

# Start production server
npm start

# Clean build directory
npm run clean
```

### Project Structure

```
src/
├── config/          # Configuration management
├── middleware/      # Express middleware
├── routes/          # API route definitions
├── services/        # Business logic services
├── types/           # TypeScript type definitions
├── utils/           # Utility functions
└── server.ts        # Main server file
```

## Deployment

### Docker

Create a `Dockerfile`:

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3000
CMD ["node", "dist/server.js"]
```

### Environment Variables for Production

Ensure these are set in your production environment:
- Set `NODE_ENV=production`
- Use production Namecheap API credentials
- Configure appropriate rate limits
- Set up proper logging
- Configure CORS for your domain

## Monitoring

The application provides several monitoring endpoints:

- `/health` - Overall health status
- `/health/metrics` - Application metrics
- `/domains/cache/stats` - Cache statistics

Logs are structured JSON format in production, making them suitable for log aggregation systems like ELK stack or Splunk.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

ISC License

## Support

For issues and questions, please create an issue in the repository.
