{"name": "domain-search", "version": "1.0.0", "description": "Node.js TypeScript application for domain name searching using Namecheap API", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "concurrently \"tsc -w\" \"nodemon dist/server.js\"", "dev:ts": "ts-node -r tsconfig-paths/register src/server.ts", "clean": "rm -rf dist", "prebuild": "npm run clean", "lint": "echo \"<PERSON><PERSON> not configured yet\"", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["domain", "search", "namecheap", "api", "typescript", "express", "nodejs", "rest-api"], "author": "", "license": "ISC", "engines": {"node": ">=16.0.0"}, "dependencies": {"axios": "^1.11.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "joi": "^18.0.1", "morgan": "^1.10.1", "node-cache": "^5.1.2", "winston": "^3.17.0"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/joi": "^17.2.2", "@types/morgan": "^1.9.10", "@types/node": "^24.3.0", "concurrently": "^9.2.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.9.2"}}