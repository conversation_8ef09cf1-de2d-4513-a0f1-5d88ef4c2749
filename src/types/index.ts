/**
 * Type Definitions Index
 * Central export point for all type definitions
 */

// Namecheap API Types
export * from './namecheap';

// Internal API Types
export * from './api';

// Environment Configuration Types
export interface EnvironmentConfig {
  NODE_ENV: 'development' | 'production' | 'test';
  PORT: number;
  
  // Namecheap API Configuration
  NAMECHEAP_API_USER: string;
  NAMECHEAP_API_KEY: string;
  NAMECHEAP_USERNAME: string;
  NAMECHEAP_CLIENT_IP: string;
  NAMECHEAP_SANDBOX: boolean;
  
  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: number;
  RATE_LIMIT_MAX_REQUESTS: number;
  
  // Cache Configuration
  CACHE_TTL: number;
  CACHE_MAX_KEYS: number;
  
  // Logging
  LOG_LEVEL: 'error' | 'warn' | 'info' | 'debug';
  LOG_FORMAT: 'json' | 'simple';
  
  // CORS
  CORS_ORIGIN: string | string[];
  
  // API Configuration
  API_TIMEOUT: number;
  API_RETRY_ATTEMPTS: number;
  API_RETRY_DELAY: number;
}

// Application Configuration
export interface AppConfig {
  env: EnvironmentConfig;
  namecheap: {
    apiUser: string;
    apiKey: string;
    userName: string;
    clientIp: string;
    sandbox: boolean;
    timeout: number;
    retryAttempts: number;
    retryDelay: number;
  };
  server: {
    port: number;
    cors: {
      origin: any;
      credentials: boolean;
    };
  };
  rateLimit: {
    windowMs: number;
    maxRequests: number;
  };
  cache: {
    ttl: number;
    maxKeys: number;
    checkPeriod: number;
  };
  logging: {
    level: string;
    format: string;
  };
}

// HTTP Status Codes
export enum HttpStatusCode {
  OK = 200,
  CREATED = 201,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  METHOD_NOT_ALLOWED = 405,
  CONFLICT = 409,
  UNPROCESSABLE_ENTITY = 422,
  TOO_MANY_REQUESTS = 429,
  INTERNAL_SERVER_ERROR = 500,
  BAD_GATEWAY = 502,
  SERVICE_UNAVAILABLE = 503,
  GATEWAY_TIMEOUT = 504
}

// Common Error Codes
export enum ErrorCode {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  DOMAIN_INVALID = 'DOMAIN_INVALID',
  API_ERROR = 'API_ERROR',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  NAMECHEAP_API_ERROR = 'NAMECHEAP_API_ERROR',
  CACHE_ERROR = 'CACHE_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  NOT_FOUND = 'NOT_FOUND'
}
