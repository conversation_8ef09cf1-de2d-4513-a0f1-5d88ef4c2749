/**
 * Namecheap API Types
 * Comprehensive type definitions for Namecheap API requests and responses
 */

// Base API Response Structure
export interface NamecheapApiResponse<T = any> {
  ApiResponse: {
    Status: 'OK' | 'ERROR';
    Errors?: {
      Error: Array<{
        Number: string;
        Description: string;
      }>;
    };
    Warnings?: {
      Warning: Array<{
        Number: string;
        Description: string;
      }>;
    };
    RequestedCommand: string;
    CommandResponse: T;
    Server: string;
    GMTTimeDifference: string;
    ExecutionTime: string;
  };
}

// Domain Check Request Parameters
export interface DomainCheckRequest {
  DomainList: string; // Comma-separated list of domains
}

// Domain Check Response
export interface DomainCheckResult {
  Domain: string;
  Available: boolean;
  ErrorNo?: string;
  Description?: string;
  IsPremiumName?: boolean;
  PremiumRegistrationPrice?: string;
  PremiumRenewalPrice?: string;
  PremiumRestorePrice?: string;
  PremiumTransferPrice?: string;
  IcannFee?: string;
  EapFee?: string;
}

export interface DomainCheckCommandResponse {
  DomainCheckResult: DomainCheckResult[];
}

// Domain Pricing Types
export interface DomainPricing {
  Duration: number;
  DurationType: 'YEAR';
  Price: number;
  PricingType: 'MULTIPLE' | 'SINGLE';
  AdditionalCost: number;
  RegularPrice: number;
  RegularPriceType: 'MULTIPLE' | 'SINGLE';
  RegularAdditionalCost: number;
  RegularAdditionalCostType: 'MULTIPLE' | 'SINGLE';
  YourPrice: number;
  YourPriceType: 'MULTIPLE' | 'SINGLE';
  YourAdditionalCost: number;
  PromotionPrice: number;
  Currency: string;
}

export interface TldPricing {
  Name: string;
  IsApiTld: boolean;
  Categories: string[];
  Pricing: {
    registration: DomainPricing[];
    renewal: DomainPricing[];
    transfer: DomainPricing[];
    restore: DomainPricing[];
  };
}

// User Pricing Response
export interface UserPricingCommandResponse {
  UserGetPricingResult: {
    ProductType: string;
    ProductCategory: string;
    Product: TldPricing[];
  };
}

// Common TLD Extensions
export type CommonTLD = 
  | '.com' 
  | '.net' 
  | '.org' 
  | '.info' 
  | '.biz' 
  | '.us' 
  | '.co' 
  | '.io' 
  | '.me' 
  | '.tv' 
  | '.cc' 
  | '.ws' 
  | '.mobi' 
  | '.name';

// API Configuration
export interface NamecheapConfig {
  apiUser: string;
  apiKey: string;
  userName: string;
  clientIp: string;
  sandbox?: boolean;
}

// Error Types
export interface NamecheapError {
  code: string;
  message: string;
  details?: string;
}
