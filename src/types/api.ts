/**
 * API Types
 * Type definitions for internal API requests and responses
 */

import { Request } from 'express';

// Domain Search Request
export interface DomainSearchRequest {
  domains: string[];
  includePricing?: boolean;
  tlds?: string[];
}

// Domain Search Response
export interface DomainSearchResponse {
  success: boolean;
  data?: DomainResult[];
  error?: ApiError;
  meta?: {
    totalDomains: number;
    availableDomains: number;
    unavailableDomains: number;
    processingTime: number;
  };
}

// Individual Domain Result
export interface DomainResult {
  domain: string;
  tld: string;
  available: boolean;
  premium?: boolean;
  pricing?: DomainPricingInfo;
  error?: string;
}

// Domain Pricing Information
export interface DomainPricingInfo {
  registration: PriceInfo;
  renewal: PriceInfo;
  transfer: PriceInfo;
  currency: string;
}

export interface PriceInfo {
  price: number;
  duration: number;
  durationType: 'YEAR';
}

// API Error Response
export interface ApiError {
  code: string;
  message: string;
  details?: string | undefined;
  timestamp: string;
}

// API Success Response
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
  meta?: Record<string, any>;
}

// Health Check Response
export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  version: string;
  uptime: number;
  services: {
    namecheap: 'connected' | 'disconnected' | 'error';
    cache: 'active' | 'inactive';
  };
}

// Request with User Context
export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
  };
}

// Validation Error
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

// Rate Limit Info
export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: number;
  retryAfter?: number;
}

// Cache Configuration
export interface CacheConfig {
  ttl: number; // Time to live in seconds
  maxKeys: number;
  checkPeriod: number;
}

// Logger Configuration
export interface LoggerConfig {
  level: 'error' | 'warn' | 'info' | 'debug';
  format: 'json' | 'simple';
  file?: {
    enabled: boolean;
    filename: string;
    maxSize: string;
    maxFiles: number;
  };
  console?: {
    enabled: boolean;
    colorize: boolean;
  };
}
