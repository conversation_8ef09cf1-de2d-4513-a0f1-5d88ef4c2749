/**
 * Health Check Routes
 * API endpoints for application health monitoring
 */

import { Router, Request, Response } from 'express';
import { asyncHandler } from '@/middleware/error.middleware';
import { DomainService } from '@/services/domain.service';
import { HealthCheckResponse, HttpStatusCode, ApiResponse } from '@/types';
import { logger } from '@/utils/logger';

const router = Router();

/**
 * GET /health
 * Basic health check endpoint
 */
router.get('/',
  asyncHandler(async (req: Request, res: Response) => {
    const startTime = Date.now();

    // Get domain service from global services
    const domainService: DomainService = (global as any).services?.domainService;

    let servicesStatus: { namecheap: string; cache: string } = {
      namecheap: 'disconnected',
      cache: 'inactive'
    };

    // Check service health if available
    if (domainService) {
      try {
        servicesStatus = await domainService.getHealthStatus();
      } catch (error) {
        logger.error('Health check service status failed', error);
        servicesStatus = {
          namecheap: 'error',
          cache: 'inactive'
        };
      }
    }

    const uptime = process.uptime();
    const processingTime = Date.now() - startTime;

    const healthResponse: HealthCheckResponse = {
      status: servicesStatus.namecheap === 'connected' ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      uptime,
      services: {
        namecheap: servicesStatus.namecheap as 'connected' | 'disconnected' | 'error',
        cache: servicesStatus.cache as 'active' | 'inactive'
      }
    };

    const statusCode = healthResponse.status === 'healthy' 
      ? HttpStatusCode.OK 
      : HttpStatusCode.SERVICE_UNAVAILABLE;

    logger.debug('Health check completed', {
      status: healthResponse.status,
      uptime,
      processingTime,
      services: servicesStatus
    });

    res.status(statusCode).json({
      success: healthResponse.status === 'healthy',
      data: healthResponse
    });
  })
);

/**
 * GET /health/ready
 * Readiness probe for Kubernetes/Docker
 */
router.get('/ready',
  asyncHandler(async (req: Request, res: Response) => {
    // Get domain service from global services
    const domainService: DomainService = (global as any).services?.domainService;

    if (!domainService) {
      return res.status(HttpStatusCode.SERVICE_UNAVAILABLE).json({
        success: false,
        data: {
          ready: false,
          reason: 'Services not initialized'
        }
      });
    }

    try {
      const servicesStatus = await domainService.getHealthStatus();
      const isReady = servicesStatus.namecheap === 'connected';

      const statusCode = isReady 
        ? HttpStatusCode.OK 
        : HttpStatusCode.SERVICE_UNAVAILABLE;

      res.status(statusCode).json({
        success: isReady,
        data: {
          ready: isReady,
          services: servicesStatus,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      logger.error('Readiness check failed', error);
      
      res.status(HttpStatusCode.SERVICE_UNAVAILABLE).json({
        success: false,
        data: {
          ready: false,
          reason: 'Service health check failed',
          timestamp: new Date().toISOString()
        }
      });
    }
  })
);

/**
 * GET /health/live
 * Liveness probe for Kubernetes/Docker
 */
router.get('/live',
  asyncHandler(async (req: Request, res: Response) => {
    // Simple liveness check - if the server can respond, it's alive
    const response: ApiResponse = {
      success: true,
      data: {
        alive: true,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        pid: process.pid
      }
    };

    res.status(HttpStatusCode.OK).json(response);
  })
);

/**
 * GET /health/metrics
 * Basic metrics endpoint for monitoring
 */
router.get('/metrics',
  asyncHandler(async (req: Request, res: Response) => {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    // Get domain service from global services
    const domainService: DomainService = (global as any).services?.domainService;
    
    let cacheStats = {};
    if (domainService) {
      try {
        cacheStats = domainService.getCacheStats();
      } catch (error) {
        logger.warn('Failed to get cache stats for metrics', error);
      }
    }

    const metrics = {
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: {
        rss: memoryUsage.rss,
        heapTotal: memoryUsage.heapTotal,
        heapUsed: memoryUsage.heapUsed,
        external: memoryUsage.external,
        arrayBuffers: memoryUsage.arrayBuffers
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system
      },
      process: {
        pid: process.pid,
        version: process.version,
        platform: process.platform,
        arch: process.arch
      },
      cache: cacheStats,
      environment: {
        nodeEnv: process.env.NODE_ENV,
        version: '1.0.0'
      }
    };

    const response: ApiResponse = {
      success: true,
      data: metrics
    };

    res.status(HttpStatusCode.OK).json(response);
  })
);

export default router;
