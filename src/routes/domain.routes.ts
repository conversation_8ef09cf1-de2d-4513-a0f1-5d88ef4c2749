/**
 * Domain Routes
 * API endpoints for domain search functionality
 */

import { Router, Request, Response } from 'express';
import { 
  validate, 
  validateQuery,
  domainSearchSchema, 
  queryParamsSchema 
} from '@/middleware/validation.middleware';
import { async<PERSON>and<PERSON> } from '@/middleware/error.middleware';
import { DomainService } from '@/services/domain.service';
import { 
  DomainSearchRequest, 
  DomainSearchResponse,
  HttpStatusCode,
  ApiResponse 
} from '@/types';
import { logger } from '@/utils/logger';

const router = Router();

/**
 * GET /api/v1/domains/search
 * Search for domain availability
 */
router.post('/search', 
  validate(domainSearchSchema),
  asyncHandler(async (req: Request, res: Response) => {
    const searchRequest: DomainSearchRequest = req.body;
    const requestId = req.get('X-Request-ID');

    logger.info('Domain search request received', {
      requestId,
      domains: searchRequest.domains.length,
      includePricing: searchRequest.includePricing,
      tlds: searchRequest.tlds?.length || 0
    });

    // Get domain service from global services
    const domainService: DomainService = (global as any).services.domainService;

    // Perform domain search
    const result: DomainSearchResponse = await domainService.searchDomains(searchRequest);

    if (result.success) {
      logger.info('Domain search completed successfully', {
        requestId,
        totalDomains: result.meta?.totalDomains,
        availableDomains: result.meta?.availableDomains,
        processingTime: result.meta?.processingTime
      });

      res.status(HttpStatusCode.OK).json(result);
    } else {
      logger.warn('Domain search failed', {
        requestId,
        error: result.error
      });

      const statusCode = result.error?.code === 'VALIDATION_ERROR' 
        ? HttpStatusCode.BAD_REQUEST 
        : HttpStatusCode.INTERNAL_SERVER_ERROR;

      res.status(statusCode).json(result);
    }
  })
);

/**
 * GET /api/v1/domains/check/:domain
 * Check availability of a single domain
 */
router.get('/check/:domain',
  asyncHandler(async (req: Request, res: Response) => {
    const domain = req.params.domain;
    const requestId = req.get('X-Request-ID');

    logger.info('Single domain check request', {
      requestId,
      domain
    });

    // Get domain service from global services
    const domainService: DomainService = (global as any).services.domainService;

    // Create search request for single domain
    const searchRequest: DomainSearchRequest = {
      domains: [domain],
      includePricing: req.query.includePricing === 'true'
    };

    const result: DomainSearchResponse = await domainService.searchDomains(searchRequest);

    if (result.success && result.data && result.data.length > 0) {
      const domainResult = result.data[0];
      
      const response: ApiResponse = {
        success: true,
        data: domainResult,
        meta: {
          processingTime: result.meta?.processingTime
        }
      };

      logger.info('Single domain check completed', {
        requestId,
        domain,
        available: domainResult.available
      });

      res.status(HttpStatusCode.OK).json(response);
    } else {
      logger.warn('Single domain check failed', {
        requestId,
        domain,
        error: result.error
      });

      const statusCode = result.error?.code === 'VALIDATION_ERROR' 
        ? HttpStatusCode.BAD_REQUEST 
        : HttpStatusCode.INTERNAL_SERVER_ERROR;

      res.status(statusCode).json({
        success: false,
        error: result.error
      });
    }
  })
);

/**
 * GET /api/v1/domains/suggestions/:baseDomain
 * Get domain suggestions with different TLDs
 */
router.get('/suggestions/:baseDomain',
  validateQuery(queryParamsSchema),
  asyncHandler(async (req: Request, res: Response) => {
    const baseDomain = req.params.baseDomain;
    const requestId = req.get('X-Request-ID');
    const { limit = 10 } = req.query as any;

    logger.info('Domain suggestions request', {
      requestId,
      baseDomain,
      limit
    });

    // Common TLDs for suggestions
    const commonTlds = [
      '.com', '.net', '.org', '.io', '.co', '.info', 
      '.biz', '.me', '.tv', '.cc', '.us'
    ];

    // Limit the number of suggestions
    const tlds = commonTlds.slice(0, Math.min(limit, commonTlds.length));

    // Get domain service from global services
    const domainService: DomainService = (global as any).services.domainService;

    // Create search request for domain suggestions
    const searchRequest: DomainSearchRequest = {
      domains: [baseDomain],
      tlds,
      includePricing: req.query.includePricing === 'true'
    };

    const result: DomainSearchResponse = await domainService.searchDomains(searchRequest);

    if (result.success) {
      // Filter only available domains for suggestions
      const availableDomains = result.data?.filter(domain => domain.available) || [];

      const response: ApiResponse = {
        success: true,
        data: {
          baseDomain,
          suggestions: availableDomains,
          total: availableDomains.length
        },
        meta: {
          processingTime: result.meta?.processingTime,
          totalChecked: result.data?.length || 0
        }
      };

      logger.info('Domain suggestions completed', {
        requestId,
        baseDomain,
        totalSuggestions: availableDomains.length,
        totalChecked: result.data?.length
      });

      res.status(HttpStatusCode.OK).json(response);
    } else {
      logger.warn('Domain suggestions failed', {
        requestId,
        baseDomain,
        error: result.error
      });

      const statusCode = result.error?.code === 'VALIDATION_ERROR' 
        ? HttpStatusCode.BAD_REQUEST 
        : HttpStatusCode.INTERNAL_SERVER_ERROR;

      res.status(statusCode).json({
        success: false,
        error: result.error
      });
    }
  })
);

/**
 * GET /api/v1/domains/cache/stats
 * Get cache statistics (for monitoring)
 */
router.get('/cache/stats',
  asyncHandler(async (req: Request, res: Response) => {
    const requestId = req.get('X-Request-ID');

    logger.debug('Cache stats request', { requestId });

    // Get domain service from global services
    const domainService: DomainService = (global as any).services.domainService;

    const stats = domainService.getCacheStats();

    const response: ApiResponse = {
      success: true,
      data: {
        cache: stats,
        timestamp: new Date().toISOString()
      }
    };

    res.status(HttpStatusCode.OK).json(response);
  })
);

/**
 * DELETE /api/v1/domains/cache
 * Clear cache (for administration)
 */
router.delete('/cache',
  asyncHandler(async (req: Request, res: Response) => {
    const requestId = req.get('X-Request-ID');

    logger.info('Cache clear request', { requestId });

    // Get domain service from global services
    const domainService: DomainService = (global as any).services.domainService;

    domainService.clearCache();

    const response: ApiResponse = {
      success: true,
      data: {
        message: 'Cache cleared successfully',
        timestamp: new Date().toISOString()
      }
    };

    logger.info('Cache cleared', { requestId });

    res.status(HttpStatusCode.OK).json(response);
  })
);

export default router;
