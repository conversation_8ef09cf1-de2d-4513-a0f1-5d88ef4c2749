/**
 * Validation Middleware
 * Request validation using Joi schemas
 */

import { Request, Response, NextFunction } from 'express';
import Jo<PERSON> from 'joi';
import { AppError } from './error.middleware';
import { HttpStatusCode, ErrorCode } from '@/types';
import { validateDomain } from '@/utils/domain';

/**
 * Generic validation middleware factory
 */
export function validate(schema: Joi.ObjectSchema) {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const validationError = new AppError(
        'Validation failed',
        HttpStatusCode.BAD_REQUEST,
        ErrorCode.VALIDATION_ERROR
      );

      // Add detailed validation errors
      const details = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value
      }));

      const errorResponse = {
        code: validationError.code,
        message: validationError.message,
        details,
        timestamp: new Date().toISOString()
      };

      res.status(HttpStatusCode.BAD_REQUEST).json({
        success: false,
        error: errorResponse
      });
      return;
    }

    // Replace request body with validated and sanitized data
    req.body = value;
    next();
  };
}

/**
 * Custom Joi validator for domain names
 */
const domainValidator = Joi.extend((joi) => ({
  type: 'domain',
  base: joi.string(),
  messages: {
    'domain.invalid': '{{#label}} must be a valid domain name'
  },
  validate(value, helpers) {
    if (!validateDomain(value)) {
      return { value, errors: helpers.error('domain.invalid') };
    }
    return { value: value.toLowerCase().trim() };
  }
}));

/**
 * Domain search request validation schema
 */
const domainSearchSchemaDefinition = Joi.object({
  domains: Joi.array()
    .items(domainValidator.domain().required())
    .min(1)
    .max(50)
    .required()
    .messages({
      'array.min': 'At least one domain is required',
      'array.max': 'Maximum 50 domains allowed per request'
    }),
  
  includePricing: Joi.boolean()
    .default(false)
    .messages({
      'boolean.base': 'includePricing must be a boolean value'
    }),
  
  tlds: Joi.array()
    .items(
      Joi.string()
        .pattern(/^\.[a-z]{2,}$/)
        .messages({
          'string.pattern.base': 'TLD must start with a dot and contain only lowercase letters'
        })
    )
    .max(20)
    .messages({
      'array.max': 'Maximum 20 TLDs allowed'
    })
});

/**
 * Query parameter validation schema
 */
const queryParamsSchemaDefinition = Joi.object({
  page: Joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1'
    }),
  
  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(20)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100'
    }),
  
  sort: Joi.string()
    .valid('domain', 'available', 'tld')
    .default('domain')
    .messages({
      'any.only': 'Sort must be one of: domain, available, tld'
    }),
  
  order: Joi.string()
    .valid('asc', 'desc')
    .default('asc')
    .messages({
      'any.only': 'Order must be either asc or desc'
    })
});

/**
 * Validate query parameters
 */
export function validateQuery(schema: Joi.ObjectSchema) {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      stripUnknown: true,
      convert: true
    });

    if (error) {
      const validationError = new AppError(
        'Invalid query parameters',
        HttpStatusCode.BAD_REQUEST,
        ErrorCode.VALIDATION_ERROR
      );

      const details = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value
      }));

      const errorResponse = {
        code: validationError.code,
        message: validationError.message,
        details,
        timestamp: new Date().toISOString()
      };

      res.status(HttpStatusCode.BAD_REQUEST).json({
        success: false,
        error: errorResponse
      });
      return;
    }

    // Replace query with validated data
    req.query = value;
    next();
  };
}

/**
 * Sanitize input middleware
 */
export function sanitizeInput(req: Request, res: Response, next: NextFunction) {
  // Recursively sanitize object
  function sanitize(obj: any): any {
    if (typeof obj === 'string') {
      return obj.trim();
    } else if (Array.isArray(obj)) {
      return obj.map(sanitize);
    } else if (obj && typeof obj === 'object') {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = sanitize(value);
      }
      return sanitized;
    }
    return obj;
  }

  // Sanitize request body
  if (req.body) {
    req.body = sanitize(req.body);
  }

  // Sanitize query parameters
  if (req.query) {
    req.query = sanitize(req.query);
  }

  next();
}

/**
 * Content type validation middleware
 */
export function validateContentType(allowedTypes: string[] = ['application/json']) {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (req.method === 'GET' || req.method === 'HEAD') {
      return next();
    }

    const contentType = req.get('Content-Type');
    
    if (!contentType) {
      const error = new AppError(
        'Content-Type header is required',
        HttpStatusCode.BAD_REQUEST,
        ErrorCode.VALIDATION_ERROR
      );
      return next(error);
    }

    const isValidType = allowedTypes.some(type => 
      contentType.toLowerCase().includes(type.toLowerCase())
    );

    if (!isValidType) {
      const error = new AppError(
        `Invalid Content-Type. Allowed types: ${allowedTypes.join(', ')}`,
        HttpStatusCode.BAD_REQUEST,
        ErrorCode.VALIDATION_ERROR
      );
      return next(error);
    }

    next();
  };
}

// Export validation schemas
export {
  domainValidator,
  domainSearchSchemaDefinition as domainSearchSchema,
  queryParamsSchemaDefinition as queryParamsSchema
};
