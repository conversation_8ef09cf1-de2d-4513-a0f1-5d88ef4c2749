/**
 * Error Handling Middleware
 * Centralized error handling for Express application
 */

import { Request, Response, NextFunction } from 'express';
import { ApiError, HttpStatusCode, ErrorCode } from '@/types';
import { logger } from '@/utils/logger';

/**
 * Custom error class for application errors
 */
export class AppError extends Error {
  public readonly statusCode: number;
  public readonly code: string;
  public readonly isOperational: boolean;

  constructor(
    message: string,
    statusCode: number = HttpStatusCode.INTERNAL_SERVER_ERROR,
    code: string = ErrorCode.INTERNAL_ERROR,
    isOperational: boolean = true
  ) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Create standardized API error response
 */
function createErrorResponse(
  error: Error | AppError,
  statusCode: number = HttpStatusCode.INTERNAL_SERVER_ERROR
): ApiError {
  const isAppError = error instanceof AppError;
  
  return {
    code: isAppError ? error.code : ErrorCode.INTERNAL_ERROR,
    message: error.message || 'An unexpected error occurred',
    details: process.env.NODE_ENV === 'development' ? error.stack : undefined,
    timestamp: new Date().toISOString()
  };
}

/**
 * Global error handling middleware
 */
export function errorHandler(
  error: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  // Log the error
  logger.error('Unhandled error', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // Determine status code
  let statusCode = HttpStatusCode.INTERNAL_SERVER_ERROR;
  
  if (error instanceof AppError) {
    statusCode = error.statusCode;
  } else if (error.name === 'ValidationError') {
    statusCode = HttpStatusCode.BAD_REQUEST;
  } else if (error.name === 'UnauthorizedError') {
    statusCode = HttpStatusCode.UNAUTHORIZED;
  } else if (error.name === 'CastError') {
    statusCode = HttpStatusCode.BAD_REQUEST;
  }

  // Create error response
  const errorResponse = createErrorResponse(error, statusCode);

  // Send error response
  res.status(statusCode).json({
    success: false,
    error: errorResponse
  });
}

/**
 * Handle 404 errors (route not found)
 */
export function notFoundHandler(req: Request, res: Response, next: NextFunction): void {
  const error = new AppError(
    `Route ${req.originalUrl} not found`,
    HttpStatusCode.NOT_FOUND,
    ErrorCode.NOT_FOUND
  );
  
  next(error);
}

/**
 * Async error wrapper for route handlers
 */
export function asyncHandler<T extends Request, U extends Response>(
  fn: (req: T, res: U, next: NextFunction) => Promise<any>
) {
  return (req: T, res: U, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

/**
 * Validation error handler
 */
export function validationErrorHandler(
  error: any,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  if (error.isJoi || error.name === 'ValidationError') {
    const validationError = new AppError(
      'Validation failed',
      HttpStatusCode.BAD_REQUEST,
      ErrorCode.VALIDATION_ERROR
    );
    
    // Add validation details
    const errorResponse = createErrorResponse(validationError);
    errorResponse.details = error.details || error.message;
    
    logger.warn('Validation error', {
      error: error.message,
      details: error.details,
      url: req.url,
      method: req.method
    });
    
    res.status(HttpStatusCode.BAD_REQUEST).json({
      success: false,
      error: errorResponse
    });
    
    return;
  }
  
  next(error);
}

/**
 * Rate limit error handler
 */
export function rateLimitErrorHandler(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  const error = new AppError(
    'Too many requests, please try again later',
    HttpStatusCode.TOO_MANY_REQUESTS,
    ErrorCode.RATE_LIMIT_EXCEEDED
  );
  
  logger.warn('Rate limit exceeded', {
    ip: req.ip,
    url: req.url,
    method: req.method,
    userAgent: req.get('User-Agent')
  });
  
  const errorResponse = createErrorResponse(error, HttpStatusCode.TOO_MANY_REQUESTS);
  
  res.status(HttpStatusCode.TOO_MANY_REQUESTS).json({
    success: false,
    error: errorResponse
  });
}

/**
 * Timeout error handler
 */
export function timeoutHandler(timeout: number = 30000) {
  return (req: Request, res: Response, next: NextFunction) => {
    const timer = setTimeout(() => {
      const error = new AppError(
        'Request timeout',
        HttpStatusCode.GATEWAY_TIMEOUT,
        ErrorCode.TIMEOUT_ERROR
      );
      next(error);
    }, timeout);

    // Clear timeout when response finishes
    res.on('finish', () => clearTimeout(timer));
    res.on('close', () => clearTimeout(timer));

    next();
  };
}

// Export error types and utilities
export { AppError as default, createErrorResponse };
