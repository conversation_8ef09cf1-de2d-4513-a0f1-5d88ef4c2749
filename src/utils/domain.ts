/**
 * Domain Utility Functions
 * Helper functions for domain validation and manipulation
 */

import { CommonTLD } from '@/types';

/**
 * Validate domain name format
 */
export function validateDomain(domain: string): boolean {
  if (!domain || typeof domain !== 'string') {
    return false;
  }

  // Remove leading/trailing whitespace
  domain = domain.trim().toLowerCase();

  // Check length
  if (domain.length < 3 || domain.length > 253) {
    return false;
  }

  // Basic domain regex pattern
  const domainRegex = /^[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?(\.[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/;
  
  if (!domainRegex.test(domain)) {
    return false;
  }

  // Check for valid TLD
  const tld = extractTld(domain);
  if (!tld || tld.length < 2) {
    return false;
  }

  // Ensure domain has at least one dot
  if (!domain.includes('.')) {
    return false;
  }

  // Check each label
  const labels = domain.split('.');
  for (const label of labels) {
    if (label.length === 0 || label.length > 63) {
      return false;
    }
    
    // Labels cannot start or end with hyphens
    if (label.startsWith('-') || label.endsWith('-')) {
      return false;
    }
  }

  return true;
}

/**
 * Extract TLD from domain name
 */
export function extractTld(domain: string): string {
  if (!domain || typeof domain !== 'string') {
    return '';
  }

  const parts = domain.toLowerCase().trim().split('.');
  if (parts.length < 2) {
    return '';
  }

  return `.${parts[parts.length - 1]}`;
}

/**
 * Extract base domain (without TLD)
 */
export function extractBaseDomain(domain: string): string {
  if (!domain || typeof domain !== 'string') {
    return '';
  }

  const parts = domain.toLowerCase().trim().split('.');
  if (parts.length < 2) {
    return domain;
  }

  return parts.slice(0, -1).join('.');
}

/**
 * Normalize domain name
 */
export function normalizeDomain(domain: string): string {
  if (!domain || typeof domain !== 'string') {
    return '';
  }

  return domain.toLowerCase().trim();
}

/**
 * Check if TLD is common/popular
 */
export function isCommonTld(tld: string): boolean {
  const commonTlds: CommonTLD[] = [
    '.com', '.net', '.org', '.info', '.biz', '.us', 
    '.co', '.io', '.me', '.tv', '.cc', '.ws', '.mobi', '.name'
  ];
  
  return commonTlds.includes(tld.toLowerCase() as CommonTLD);
}

/**
 * Generate domain suggestions with different TLDs
 */
export function generateDomainSuggestions(baseDomain: string, tlds?: string[]): string[] {
  if (!baseDomain) {
    return [];
  }

  const defaultTlds = ['.com', '.net', '.org', '.io', '.co'];
  const targetTlds = tlds && tlds.length > 0 ? tlds : defaultTlds;
  
  const base = extractBaseDomain(baseDomain);
  
  return targetTlds.map(tld => {
    const normalizedTld = tld.startsWith('.') ? tld : `.${tld}`;
    return `${base}${normalizedTld}`;
  });
}

/**
 * Check if domain is an IDN (Internationalized Domain Name)
 */
export function isIdnDomain(domain: string): boolean {
  if (!domain) {
    return false;
  }

  // Check for punycode prefix
  return domain.includes('xn--') || /[^\x00-\x7F]/.test(domain);
}

/**
 * Validate domain list
 */
export function validateDomainList(domains: string[]): { valid: string[]; invalid: string[] } {
  const valid: string[] = [];
  const invalid: string[] = [];

  for (const domain of domains) {
    if (validateDomain(domain)) {
      valid.push(normalizeDomain(domain));
    } else {
      invalid.push(domain);
    }
  }

  return { valid, invalid };
}

/**
 * Check if domain is a subdomain
 */
export function isSubdomain(domain: string): boolean {
  if (!domain) {
    return false;
  }

  const parts = domain.split('.');
  return parts.length > 2;
}

/**
 * Get domain levels
 */
export function getDomainLevels(domain: string): {
  tld: string;
  sld: string; // Second Level Domain
  subdomain?: string;
} {
  if (!domain) {
    return { tld: '', sld: '' };
  }

  const parts = domain.toLowerCase().split('.');
  
  if (parts.length < 2) {
    return { tld: '', sld: domain };
  }

  const tld = `.${parts[parts.length - 1]}`;
  const sld = parts[parts.length - 2];
  const subdomain = parts.length > 2 ? parts.slice(0, -2).join('.') : undefined;

  return { tld, sld, subdomain };
}

/**
 * Common TLD list for reference
 */
export const COMMON_TLDS: CommonTLD[] = [
  '.com', '.net', '.org', '.info', '.biz', '.us', 
  '.co', '.io', '.me', '.tv', '.cc', '.ws', '.mobi', '.name'
];

/**
 * Popular TLD categories
 */
export const TLD_CATEGORIES = {
  generic: ['.com', '.net', '.org', '.info', '.biz'],
  country: ['.us', '.uk', '.ca', '.au', '.de', '.fr'],
  new: ['.io', '.co', '.me', '.tv', '.app', '.dev'],
  business: ['.biz', '.company', '.corp', '.inc'],
  tech: ['.io', '.tech', '.dev', '.app', '.ai']
};
