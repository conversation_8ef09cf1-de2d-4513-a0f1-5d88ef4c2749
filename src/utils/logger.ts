/**
 * Logger Utility
 * Centralized logging configuration using Winston
 */

import winston from 'winston';
import { LoggerConfig } from '@/types';

// Default logger configuration
const defaultConfig: LoggerConfig = {
  level: process.env.LOG_LEVEL as any || 'info',
  format: process.env.LOG_FORMAT as any || 'json',
  console: {
    enabled: true,
    colorize: process.env.NODE_ENV !== 'production'
  },
  file: {
    enabled: process.env.NODE_ENV === 'production',
    filename: 'logs/app.log',
    maxSize: '10m',
    maxFiles: 5
  }
};

/**
 * Create Winston logger instance
 */
function createLogger(config: LoggerConfig = defaultConfig): winston.Logger {
  const formats: winston.Logform.Format[] = [
    winston.format.timestamp(),
    winston.format.errors({ stack: true })
  ];

  // Add format based on configuration
  if (config.format === 'json') {
    formats.push(winston.format.json());
  } else {
    formats.push(
      winston.format.printf(({ timestamp, level, message, ...meta }) => {
        const metaStr = Object.keys(meta).length ? JSON.stringify(meta) : '';
        return `${timestamp} [${level.toUpperCase()}]: ${message} ${metaStr}`;
      })
    );
  }

  const transports: winston.transport[] = [];

  // Console transport
  if (config.console?.enabled) {
    transports.push(
      new winston.transports.Console({
        format: winston.format.combine(
          config.console.colorize ? winston.format.colorize() : winston.format.uncolorize(),
          ...formats
        )
      })
    );
  }

  // File transport
  if (config.file?.enabled) {
    transports.push(
      new winston.transports.File({
        filename: config.file.filename,
        maxsize: parseSize(config.file.maxSize),
        maxFiles: config.file.maxFiles,
        format: winston.format.combine(...formats)
      })
    );
  }

  return winston.createLogger({
    level: config.level,
    transports,
    exitOnError: false,
    // Handle uncaught exceptions and rejections
    exceptionHandlers: config.file?.enabled ? [
      new winston.transports.File({ filename: 'logs/exceptions.log' })
    ] : [],
    rejectionHandlers: config.file?.enabled ? [
      new winston.transports.File({ filename: 'logs/rejections.log' })
    ] : []
  });
}

/**
 * Parse size string to bytes
 */
function parseSize(size: string): number {
  const units: { [key: string]: number } = {
    b: 1,
    k: 1024,
    m: 1024 * 1024,
    g: 1024 * 1024 * 1024
  };

  const match = size.toLowerCase().match(/^(\d+)([bkmg]?)$/);
  if (!match) {
    throw new Error(`Invalid size format: ${size}`);
  }

  const value = parseInt(match[1], 10);
  const unit = match[2] || 'b';

  return value * units[unit];
}

// Create and export the default logger instance
export const logger = createLogger();

// Export logger creation function for custom configurations
export { createLogger };

// Export logger types
export type { LoggerConfig };
