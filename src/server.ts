/**
 * Express Server
 * Main server setup with middleware and route configuration
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import { config } from '@/config';
import { logger } from '@/utils/logger';
import { 
  error<PERSON><PERSON><PERSON>, 
  notFoundHandler, 
  validationErrorHandler,
  rateLimitErrorHandler,
  timeoutHandler 
} from '@/middleware/error.middleware';
import { sanitizeInput, validateContentType } from '@/middleware/validation.middleware';
import { NamecheapService } from '@/services/namecheap.service';
import { DomainService } from '@/services/domain.service';
import domainRoutes from '@/routes/domain.routes';
import healthRoutes from '@/routes/health.routes';

/**
 * Create Express application
 */
function createApp(): express.Application {
  const app = express();

  // Trust proxy for accurate IP addresses
  app.set('trust proxy', 1);

  // Security middleware
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true
    }
  }));

  // CORS configuration
  app.use(cors({
    origin: config.server.cors.origin,
    credentials: config.server.cors.credentials,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    exposedHeaders: ['X-RateLimit-Limit', 'X-RateLimit-Remaining', 'X-RateLimit-Reset']
  }));

  // Rate limiting
  const limiter = rateLimit({
    windowMs: config.rateLimit.windowMs,
    max: config.rateLimit.maxRequests,
    message: {
      success: false,
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        message: 'Too many requests, please try again later',
        timestamp: new Date().toISOString()
      }
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: rateLimitErrorHandler,
    skip: (req) => {
      // Skip rate limiting for health checks
      return req.path === '/health' || req.path === '/health/ready';
    }
  });

  app.use(limiter);

  // Request timeout
  app.use(timeoutHandler(30000)); // 30 seconds

  // Body parsing middleware
  app.use(express.json({ 
    limit: '10mb',
    strict: true
  }));
  app.use(express.urlencoded({ 
    extended: true, 
    limit: '10mb' 
  }));

  // Content type validation
  app.use(validateContentType(['application/json']));

  // Input sanitization
  app.use(sanitizeInput);

  // Logging middleware
  const morganFormat = config.env.NODE_ENV === 'production' 
    ? 'combined' 
    : 'dev';

  app.use(morgan(morganFormat, {
    stream: {
      write: (message: string) => {
        logger.info(message.trim(), { source: 'http' });
      }
    },
    skip: (req) => {
      // Skip logging for health checks in production
      return config.env.NODE_ENV === 'production' && 
             (req.path === '/health' || req.path === '/health/ready');
    }
  }));

  // Request ID middleware
  app.use((req, res, next) => {
    const requestId = req.get('X-Request-ID') || 
                     `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    req.headers['x-request-id'] = requestId;
    res.set('X-Request-ID', requestId);
    next();
  });

  // API routes
  app.use('/health', healthRoutes);
  app.use('/api/v1/domains', domainRoutes);

  // Root endpoint
  app.get('/', (req, res) => {
    res.json({
      success: true,
      message: 'Domain Search API',
      version: '1.0.0',
      documentation: '/api/v1/docs',
      health: '/health'
    });
  });

  // Validation error handler (must be before general error handler)
  app.use(validationErrorHandler);

  // 404 handler
  app.use(notFoundHandler);

  // Global error handler (must be last)
  app.use(errorHandler);

  return app;
}

/**
 * Initialize services
 */
function initializeServices(): { namecheapService: NamecheapService; domainService: DomainService } {
  logger.info('Initializing services...');

  // Initialize Namecheap service
  const namecheapService = new NamecheapService({
    apiUser: config.namecheap.apiUser,
    apiKey: config.namecheap.apiKey,
    userName: config.namecheap.userName,
    clientIp: config.namecheap.clientIp,
    sandbox: config.namecheap.sandbox
  });

  // Initialize Domain service with caching
  const domainService = new DomainService(namecheapService, config.cache);

  logger.info('Services initialized successfully');

  return { namecheapService, domainService };
}

/**
 * Start the server
 */
async function startServer(): Promise<void> {
  try {
    // Initialize services
    const { namecheapService, domainService } = initializeServices();

    // Make services available globally (for routes)
    (global as any).services = { namecheapService, domainService };

    // Create Express app
    const app = createApp();

    // Start server
    const server = app.listen(config.server.port, () => {
      logger.info(`Server started successfully`, {
        port: config.server.port,
        environment: config.env.NODE_ENV,
        sandbox: config.namecheap.sandbox
      });
    });

    // Graceful shutdown
    const gracefulShutdown = (signal: string) => {
      logger.info(`Received ${signal}, shutting down gracefully...`);
      
      server.close(() => {
        logger.info('Server closed');
        
        // Clear cache
        domainService.clearCache();
        
        process.exit(0);
      });

      // Force close after 10 seconds
      setTimeout(() => {
        logger.error('Forced shutdown after timeout');
        process.exit(1);
      }, 10000);
    };

    // Handle shutdown signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception', error);
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled rejection', { reason, promise });
      process.exit(1);
    });

  } catch (error) {
    logger.error('Failed to start server', error);
    process.exit(1);
  }
}

// Start the server if this file is run directly
if (require.main === module) {
  startServer();
}

export { createApp, startServer };
