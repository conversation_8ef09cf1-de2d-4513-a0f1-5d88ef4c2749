/**
 * Namecheap API Service
 * Handles all interactions with the Namecheap API
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { 
  NamecheapConfig, 
  NamecheapApiResponse, 
  DomainCheckCommandResponse,
  UserPricingCommandResponse,
  DomainCheckResult,
  TldPricing,
  NamecheapError,
  ErrorCode
} from '@/types';
import { logger } from '@/utils/logger';

export class NamecheapService {
  private readonly apiClient: AxiosInstance;
  private readonly config: NamecheapConfig;
  private readonly baseUrl: string;

  constructor(config: NamecheapConfig) {
    this.config = config;
    this.baseUrl = config.sandbox 
      ? 'https://api.sandbox.namecheap.com/xml.response'
      : 'https://api.namecheap.com/xml.response';

    this.apiClient = axios.create({
      baseURL: this.baseUrl,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'Domain-Search-API/1.0.0'
      }
    });

    this.setupInterceptors();
  }

  /**
   * Setup axios interceptors for logging and error handling
   */
  private setupInterceptors(): void {
    // Request interceptor
    this.apiClient.interceptors.request.use(
      (config) => {
        logger.debug('Namecheap API Request', {
          url: config.url,
          method: config.method,
          params: config.params
        });
        return config;
      },
      (error) => {
        logger.error('Namecheap API Request Error', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.apiClient.interceptors.response.use(
      (response) => {
        logger.debug('Namecheap API Response', {
          status: response.status,
          statusText: response.statusText
        });
        return response;
      },
      (error) => {
        logger.error('Namecheap API Response Error', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Check domain availability for multiple domains
   */
  async checkDomainAvailability(domains: string[]): Promise<DomainCheckResult[]> {
    try {
      const domainList = domains.join(',');
      
      const params = {
        ApiUser: this.config.apiUser,
        ApiKey: this.config.apiKey,
        UserName: this.config.userName,
        Command: 'namecheap.domains.check',
        ClientIp: this.config.clientIp,
        DomainList: domainList
      };

      logger.info('Checking domain availability', { domains: domains.length });

      const response: AxiosResponse<NamecheapApiResponse<DomainCheckCommandResponse>> = 
        await this.apiClient.post('', new URLSearchParams(params));

      const apiResponse = response.data;

      if (apiResponse.ApiResponse.Status === 'ERROR') {
        const errors = apiResponse.ApiResponse.Errors?.Error || [];
        const errorMessage = errors.map(e => e.Description).join(', ');
        throw new Error(`Namecheap API Error: ${errorMessage}`);
      }

      const results = apiResponse.ApiResponse.CommandResponse.DomainCheckResult;
      
      logger.info('Domain availability check completed', { 
        total: results.length,
        available: results.filter(r => r.Available).length
      });

      return results;
    } catch (error) {
      logger.error('Failed to check domain availability', error);
      throw this.handleError(error);
    }
  }

  /**
   * Get pricing information for TLDs
   */
  async getTldPricing(tlds?: string[]): Promise<TldPricing[]> {
    try {
      const params = {
        ApiUser: this.config.apiUser,
        ApiKey: this.config.apiKey,
        UserName: this.config.userName,
        Command: 'namecheap.users.getPricing',
        ClientIp: this.config.clientIp,
        ProductType: 'DOMAIN',
        ProductCategory: 'DOMAINS'
      };

      logger.info('Fetching TLD pricing information');

      const response: AxiosResponse<NamecheapApiResponse<UserPricingCommandResponse>> = 
        await this.apiClient.post('', new URLSearchParams(params));

      const apiResponse = response.data;

      if (apiResponse.ApiResponse.Status === 'ERROR') {
        const errors = apiResponse.ApiResponse.Errors?.Error || [];
        const errorMessage = errors.map(e => e.Description).join(', ');
        throw new Error(`Namecheap API Error: ${errorMessage}`);
      }

      let pricing = apiResponse.ApiResponse.CommandResponse.UserGetPricingResult.Product;

      // Filter by specific TLDs if provided
      if (tlds && tlds.length > 0) {
        const tldSet = new Set(tlds.map(tld => tld.toLowerCase()));
        pricing = pricing.filter(p => tldSet.has(p.Name.toLowerCase()));
      }

      logger.info('TLD pricing information retrieved', { count: pricing.length });

      return pricing;
    } catch (error) {
      logger.error('Failed to get TLD pricing', error);
      throw this.handleError(error);
    }
  }

  /**
   * Test API connection
   */
  async testConnection(): Promise<boolean> {
    try {
      // Use a simple domain check to test the connection
      await this.checkDomainAvailability(['test-connection-domain.com']);
      return true;
    } catch (error) {
      logger.error('Namecheap API connection test failed', error);
      return false;
    }
  }

  /**
   * Handle and transform errors
   */
  private handleError(error: any): NamecheapError {
    if (error.response) {
      // HTTP error response
      return {
        code: ErrorCode.NAMECHEAP_API_ERROR,
        message: `HTTP ${error.response.status}: ${error.response.statusText}`,
        details: error.response.data
      };
    } else if (error.request) {
      // Network error
      return {
        code: ErrorCode.SERVICE_UNAVAILABLE,
        message: 'Unable to connect to Namecheap API',
        details: error.message
      };
    } else if (error.code === 'ECONNABORTED') {
      // Timeout error
      return {
        code: ErrorCode.TIMEOUT_ERROR,
        message: 'Request to Namecheap API timed out',
        details: error.message
      };
    } else {
      // Other errors
      return {
        code: ErrorCode.INTERNAL_ERROR,
        message: error.message || 'Unknown error occurred',
        details: error.stack
      };
    }
  }
}
