/**
 * Domain Service
 * High-level service for domain operations with caching and error handling
 */

import NodeCache from 'node-cache';
import { NamecheapService } from './namecheap.service';
import { 
  DomainResult, 
  DomainSearchRequest, 
  DomainSearchResponse,
  DomainPricingInfo,
  PriceInfo,
  CacheConfig,
  ErrorCode,
  CommonTLD
} from '@/types';
import { logger } from '@/utils/logger';
import { validateDomain, extractTld } from '@/utils/domain';

export class DomainService {
  private readonly namecheapService: NamecheapService;
  private readonly cache: NodeCache;

  constructor(namecheapService: NamecheapService, cacheConfig: CacheConfig) {
    this.namecheapService = namecheapService;
    this.cache = new NodeCache({
      stdTTL: cacheConfig.ttl,
      maxKeys: cacheConfig.maxKeys,
      checkperiod: cacheConfig.checkPeriod,
      useClones: false
    });

    // Log cache statistics periodically
    setInterval(() => {
      const stats = this.cache.getStats();
      logger.debug('Cache statistics', stats);
    }, 300000); // Every 5 minutes
  }

  /**
   * Search for domain availability with optional pricing
   */
  async searchDomains(request: DomainSearchRequest): Promise<DomainSearchResponse> {
    const startTime = Date.now();
    
    try {
      // Validate and normalize domains
      const validatedDomains = this.validateAndNormalizeDomains(request.domains);
      
      if (validatedDomains.length === 0) {
        return {
          success: false,
          error: {
            code: ErrorCode.VALIDATION_ERROR,
            message: 'No valid domains provided',
            timestamp: new Date().toISOString()
          }
        };
      }

      // Generate domain variations if TLDs are specified
      const domainsToCheck = this.generateDomainVariations(validatedDomains, request.tlds);
      
      // Check cache first
      const { cachedResults, uncachedDomains } = this.checkCache(domainsToCheck);
      
      let apiResults: DomainResult[] = [];
      
      // Fetch uncached domains from API
      if (uncachedDomains.length > 0) {
        logger.info('Fetching domain availability from API', { 
          count: uncachedDomains.length 
        });
        
        const namecheapResults = await this.namecheapService.checkDomainAvailability(uncachedDomains);
        
        // Transform Namecheap results to our format
        apiResults = await this.transformNamecheapResults(namecheapResults, request.includePricing);
        
        // Cache the results
        this.cacheResults(apiResults);
      }

      // Combine cached and API results
      const allResults = [...cachedResults, ...apiResults];
      
      // Sort results by domain name
      allResults.sort((a, b) => a.domain.localeCompare(b.domain));

      const processingTime = Date.now() - startTime;
      
      const response: DomainSearchResponse = {
        success: true,
        data: allResults,
        meta: {
          totalDomains: allResults.length,
          availableDomains: allResults.filter(r => r.available).length,
          unavailableDomains: allResults.filter(r => !r.available).length,
          processingTime
        }
      };

      logger.info('Domain search completed', {
        totalDomains: allResults.length,
        availableDomains: response.meta?.availableDomains,
        processingTime
      });

      return response;
    } catch (error) {
      logger.error('Domain search failed', error);
      
      return {
        success: false,
        error: {
          code: ErrorCode.INTERNAL_ERROR,
          message: 'Failed to search domains',
          details: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Get health status of the service
   */
  async getHealthStatus(): Promise<{ namecheap: string; cache: string }> {
    try {
      const namecheapStatus = await this.namecheapService.testConnection();
      const cacheStats = this.cache.getStats();
      
      return {
        namecheap: namecheapStatus ? 'connected' : 'disconnected',
        cache: cacheStats.keys > 0 ? 'active' : 'inactive'
      };
    } catch (error) {
      logger.error('Health check failed', error);
      return {
        namecheap: 'error',
        cache: 'inactive'
      };
    }
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.flushAll();
    logger.info('Cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return this.cache.getStats();
  }

  /**
   * Validate and normalize domain names
   */
  private validateAndNormalizeDomains(domains: string[]): string[] {
    return domains
      .map(domain => domain.toLowerCase().trim())
      .filter(domain => {
        if (!validateDomain(domain)) {
          logger.warn('Invalid domain skipped', { domain });
          return false;
        }
        return true;
      });
  }

  /**
   * Generate domain variations with different TLDs
   */
  private generateDomainVariations(domains: string[], tlds?: string[]): string[] {
    if (!tlds || tlds.length === 0) {
      return domains;
    }

    const variations: string[] = [];
    
    for (const domain of domains) {
      const baseDomain = domain.includes('.') ? domain.split('.')[0] : domain;
      
      for (const tld of tlds) {
        const normalizedTld = tld.startsWith('.') ? tld : `.${tld}`;
        variations.push(`${baseDomain}${normalizedTld}`);
      }
    }

    return variations;
  }

  /**
   * Check cache for existing results
   */
  private checkCache(domains: string[]): { cachedResults: DomainResult[]; uncachedDomains: string[] } {
    const cachedResults: DomainResult[] = [];
    const uncachedDomains: string[] = [];

    for (const domain of domains) {
      const cacheKey = `domain:${domain}`;
      const cached = this.cache.get<DomainResult>(cacheKey);
      
      if (cached) {
        cachedResults.push(cached);
        logger.debug('Cache hit', { domain });
      } else {
        uncachedDomains.push(domain);
      }
    }

    logger.info('Cache check completed', {
      cached: cachedResults.length,
      uncached: uncachedDomains.length
    });

    return { cachedResults, uncachedDomains };
  }

  /**
   * Transform Namecheap results to our format
   */
  private async transformNamecheapResults(
    namecheapResults: any[], 
    includePricing?: boolean
  ): Promise<DomainResult[]> {
    const results: DomainResult[] = [];

    for (const result of namecheapResults) {
      const domainResult: DomainResult = {
        domain: result.Domain,
        tld: extractTld(result.Domain),
        available: result.Available,
        premium: result.IsPremiumName || false
      };

      if (result.ErrorNo) {
        domainResult.error = result.Description || 'Unknown error';
      }

      // Add pricing if requested and available
      if (includePricing && result.Available) {
        try {
          domainResult.pricing = await this.getPricingForDomain(domainResult.tld);
        } catch (error) {
          logger.warn('Failed to get pricing for domain', { 
            domain: result.Domain, 
            error: error instanceof Error ? error.message : 'Unknown error' 
          });
        }
      }

      results.push(domainResult);
    }

    return results;
  }

  /**
   * Get pricing information for a specific TLD
   */
  private async getPricingForDomain(tld: string): Promise<DomainPricingInfo | undefined> {
    try {
      const pricingData = await this.namecheapService.getTldPricing([tld]);
      
      if (pricingData.length === 0) {
        return undefined;
      }

      const tldPricing = pricingData[0];
      
      return {
        registration: this.extractPriceInfo(tldPricing.Pricing.registration),
        renewal: this.extractPriceInfo(tldPricing.Pricing.renewal),
        transfer: this.extractPriceInfo(tldPricing.Pricing.transfer),
        currency: tldPricing.Pricing.registration[0]?.Currency || 'USD'
      };
    } catch (error) {
      logger.error('Failed to get TLD pricing', { tld, error });
      return undefined;
    }
  }

  /**
   * Extract price information from Namecheap pricing data
   */
  private extractPriceInfo(pricingArray: any[]): PriceInfo {
    const defaultPrice = pricingArray.find(p => p.Duration === 1) || pricingArray[0];
    
    return {
      price: defaultPrice?.YourPrice || defaultPrice?.Price || 0,
      duration: defaultPrice?.Duration || 1,
      durationType: 'YEAR'
    };
  }

  /**
   * Cache domain results
   */
  private cacheResults(results: DomainResult[]): void {
    for (const result of results) {
      const cacheKey = `domain:${result.domain}`;
      this.cache.set(cacheKey, result);
    }
    
    logger.debug('Results cached', { count: results.length });
  }
}
