/**
 * Application Configuration
 * Centralized configuration management with environment variable validation
 */

import dotenv from 'dotenv';
import { AppConfig, EnvironmentConfig } from '@/types';

// Load environment variables
dotenv.config();

/**
 * Parse boolean environment variable
 */
function parseBoolean(value: string | undefined, defaultValue: boolean = false): boolean {
  if (!value) return defaultValue;
  return value.toLowerCase() === 'true' || value === '1';
}

/**
 * Parse number environment variable
 */
function parseNumber(value: string | undefined, defaultValue: number): number {
  if (!value) return defaultValue;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * Parse array environment variable
 */
function parseArray(value: string | undefined, defaultValue: string[] = []): string[] {
  if (!value) return defaultValue;
  return value.split(',').map(item => item.trim()).filter(Boolean);
}

/**
 * Validate required environment variables
 */
function validateRequiredEnvVars(): void {
  const required = [
    'NAMECHEAP_API_USER',
    'NAMECHEAP_API_KEY',
    'NAMECHEAP_USERNAME',
    'NAMECHEAP_CLIENT_IP'
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
}

/**
 * Load and validate environment configuration
 */
function loadEnvironmentConfig(): EnvironmentConfig {
  // Validate required variables
  validateRequiredEnvVars();

  return {
    NODE_ENV: (process.env['NODE_ENV'] as any) || 'development',
    PORT: parseNumber(process.env['PORT'], 3000),

    // Namecheap API Configuration
    NAMECHEAP_API_USER: process.env['NAMECHEAP_API_USER']!,
    NAMECHEAP_API_KEY: process.env['NAMECHEAP_API_KEY']!,
    NAMECHEAP_USERNAME: process.env['NAMECHEAP_USERNAME']!,
    NAMECHEAP_CLIENT_IP: process.env['NAMECHEAP_CLIENT_IP']!,
    NAMECHEAP_SANDBOX: parseBoolean(process.env['NAMECHEAP_SANDBOX'], true),

    // Rate Limiting
    RATE_LIMIT_WINDOW_MS: parseNumber(process.env['RATE_LIMIT_WINDOW_MS'], 15 * 60 * 1000), // 15 minutes
    RATE_LIMIT_MAX_REQUESTS: parseNumber(process.env['RATE_LIMIT_MAX_REQUESTS'], 100),

    // Cache Configuration
    CACHE_TTL: parseNumber(process.env['CACHE_TTL'], 300), // 5 minutes
    CACHE_MAX_KEYS: parseNumber(process.env['CACHE_MAX_KEYS'], 1000),

    // Logging
    LOG_LEVEL: (process.env['LOG_LEVEL'] as any) || 'info',
    LOG_FORMAT: (process.env['LOG_FORMAT'] as any) || 'json',

    // CORS
    CORS_ORIGIN: process.env['CORS_ORIGIN'] || '*',

    // API Configuration
    API_TIMEOUT: parseNumber(process.env['API_TIMEOUT'], 30000), // 30 seconds
    API_RETRY_ATTEMPTS: parseNumber(process.env['API_RETRY_ATTEMPTS'], 3),
    API_RETRY_DELAY: parseNumber(process.env['API_RETRY_DELAY'], 1000) // 1 second
  };
}

/**
 * Create application configuration
 */
function createAppConfig(): AppConfig {
  const env = loadEnvironmentConfig();

  return {
    env,
    namecheap: {
      apiUser: env.NAMECHEAP_API_USER,
      apiKey: env.NAMECHEAP_API_KEY,
      userName: env.NAMECHEAP_USERNAME,
      clientIp: env.NAMECHEAP_CLIENT_IP,
      sandbox: env.NAMECHEAP_SANDBOX,
      timeout: env.API_TIMEOUT,
      retryAttempts: env.API_RETRY_ATTEMPTS,
      retryDelay: env.API_RETRY_DELAY
    },
    server: {
      port: env.PORT,
      cors: {
        origin: env.CORS_ORIGIN === '*' ? '*' : parseArray(env.CORS_ORIGIN) as any,
        credentials: true
      }
    },
    rateLimit: {
      windowMs: env.RATE_LIMIT_WINDOW_MS,
      maxRequests: env.RATE_LIMIT_MAX_REQUESTS
    },
    cache: {
      ttl: env.CACHE_TTL,
      maxKeys: env.CACHE_MAX_KEYS,
      checkPeriod: Math.floor(env.CACHE_TTL / 2) // Check period is half of TTL
    },
    logging: {
      level: env.LOG_LEVEL,
      format: env.LOG_FORMAT
    }
  };
}

// Export the configuration
export const config = createAppConfig();

// Export individual configurations for convenience
export const {
  env: envConfig,
  namecheap: namecheapConfig,
  server: serverConfig,
  rateLimit: rateLimitConfig,
  cache: cacheConfig,
  logging: loggingConfig
} = config;

// Export configuration functions
export { loadEnvironmentConfig, createAppConfig };

// Development helper to log configuration (excluding sensitive data)
if (config.env.NODE_ENV === 'development') {
  console.log('Configuration loaded:', {
    environment: config.env.NODE_ENV,
    port: config.server.port,
    sandbox: config.namecheap.sandbox,
    logLevel: config.logging.level,
    cacheConfig: config.cache,
    rateLimitConfig: config.rateLimit
  });
}
