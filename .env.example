# Environment Configuration Template
# Copy this file to .env and fill in your actual values

# Application Environment
NODE_ENV=development
PORT=3000

# Namecheap API Configuration
# Get these credentials from your Namecheap account
NAMECHEAP_API_USER=your_api_user
NAMECHEAP_API_KEY=your_api_key
NAMECHEAP_USERNAME=your_username
NAMECHEAP_CLIENT_IP=your_whitelisted_ip
NAMECHEAP_SANDBOX=true

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Cache Configuration
CACHE_TTL=300
CACHE_MAX_KEYS=1000

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# CORS Configuration
CORS_ORIGIN=*

# API Configuration
API_TIMEOUT=30000
API_RETRY_ATTEMPTS=3
API_RETRY_DELAY=1000

# Optional: Database Configuration (for future use)
# DATABASE_URL=postgresql://user:password@localhost:5432/domain_search

# Optional: Redis Configuration (for distributed caching)
# REDIS_URL=redis://localhost:6379

# Optional: Monitoring Configuration
# SENTRY_DSN=your_sentry_dsn
# NEW_RELIC_LICENSE_KEY=your_new_relic_key
